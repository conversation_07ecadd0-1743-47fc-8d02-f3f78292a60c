"use client";
 
import {ConvexProvider } from "convex/react";
import { ConvexReactClient } from "convex/react";
import { ReactNode } from "react";
 
const convex = new ConvexReactClient(process.env.NEXT_PUBLIC_CONVEX_URL!);
 console.log(process.env.NEXT_PUBLIC_CONVEX_URL)
export function ConvexClientProvider({ children }: { children: ReactNode }) {
  return (

    <ConvexProvider client={convex}>
      {children}
    </ConvexProvider>

  );
}